---
title: "Deploy Your Website"

description: "Information about hosting, deploying, and managing your Libra projects"

mode: "center"

icon: Globe

---

# Deploy Your Website

Information about hosting, deploying, and managing your Libra projects

## Can I publish my application?

Of course you can!

You can deploy your application with one click directly in Libra.

Just click the "Publish" button in the header to complete the deployment.

If you are a Free, Pro, or Enterprise plan subscriber, you can also choose an available subdomain, such as https://demo.libra.sh

## Deployment Process

### 1. Prepare for Deployment

Before publishing, please ensure:

- Your application has been fully developed

- The application runs properly in preview mode

### 2. One-Click Publish

1. In the Libra workspace, locate the "Publish" button at the top of the page

2. Click the "Publish" button to start the deployment process

3. The system will automatically handle the build and deployment process

### 3. Choose a Domain Name

Depending on your subscription plan:

- **Free Plan Users**: Can use the default Libra subdomain

- **Pro Plan Users**: Can choose a custom subdomain

- **Enterprise Plan Users**: Can choose a custom subdomain

### 4. Deployment Completed

After deployment is complete, you will receive:

- A live access link to your application

## Managing Published Applications

After publishing, you can:

- **Update the Application**: Republish after making code changes

- **Domain Management**: Modify or upgrade your domain settings

---

*Begin your deployment journey and let the world see your creativity!*