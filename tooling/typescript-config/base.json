{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2024", "lib": ["ES2024", "DOM", "DOM.Iterable"], "allowJs": true, "checkJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "Preserve", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "jsx": "preserve", "incremental": true, "noUncheckedIndexedAccess": true, "disableSourceOfProjectReferenceRedirect": true, "allowSyntheticDefaultImports": true, "tsBuildInfoFile": "${configDir}/.cache/tsbuildinfo.json", "pretty": true, "sourceMap": false, "inlineSourceMap": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "declaration": false, "declarationMap": false}, "exclude": ["node_modules", "build", "dist", ".next", ".expo"]}