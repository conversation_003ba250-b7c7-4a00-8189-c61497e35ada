{"root": false, "$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "files": {"ignoreUnknown": true, "maxSize": 5242880, "includes": ["**", "!**/node_modules/**"]}, "formatter": {"enabled": true, "lineWidth": 100, "indentStyle": "space", "indentWidth": 2}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "single", "trailingCommas": "es5", "semicolons": "asNeeded"}}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}}