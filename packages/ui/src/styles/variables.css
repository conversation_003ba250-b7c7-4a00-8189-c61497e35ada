/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * variables.css
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

:root {
  /* Accent colors */
  --brand: oklch(66.5% 0.1804 47.04);
  --brand-foreground: oklch(75.77% 0.159 55.91);

  --background: oklch(98% 0.01 95.1);
  --foreground: oklch(34% 0.03 95.72);

  --background-landing: oklch(100% 0.01 97.5);
  --foreground-landing: oklch(32% 0.03 95.72);

  --card: oklch(98% 0.01 95.1);
  --card-foreground: oklch(19% 0 106.59);
  --popover: oklch(100% 0 0);
  --popover-foreground: oklch(27% 0.02 98.94);
  --primary: oklch(62% 0.14 39.04);
  --primary-foreground: oklch(100% 0 0);
  --secondary: oklch(92% 0.01 92.99);
  --secondary-foreground: oklch(43% 0.02 98.6);
  --muted: oklch(93% 0.02 90.24);
  --muted-foreground: oklch(61% 0.01 97.42);
  --accent: oklch(92% 0.01 92.99);
  --accent-foreground: oklch(27% 0.02 98.94);
  --destructive: oklch(19% 0 106.59);
  --destructive-foreground: oklch(100% 0 0);
  --border: oklch(88% 0.01 97.36);
  --input: oklch(76% 0.02 98.35);
  --ring: oklch(87% 0.0671 252);
  --chart-1: oklch(56% 0.13 43);
  --chart-2: oklch(69% 0.16 290.41);
  --chart-3: oklch(88% 0.03 93.13);
  --chart-4: oklch(88% 0.04 298.18);
  --chart-5: oklch(56% 0.13 42.06);
  --sidebar: oklch(97% 0.01 98.88);
  --sidebar-foreground: oklch(36% 0.01 106.65);
  --sidebar-primary: oklch(62% 0.14 39.04);
  --sidebar-primary-foreground: oklch(99% 0 0);
  --sidebar-accent: oklch(92% 0.01 92.99);
  --sidebar-accent-foreground: oklch(33% 0 0);
  --sidebar-border: oklch(94% 0 0);
  --sidebar-ring: oklch(77% 0 0);
  --font-sans: Inter, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: Mona_Sans, monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 5%);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 5%);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 10%), 0 1px 2px -1px hsl(0 0% 0% / 10%);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 10%), 0 1px 2px -1px hsl(0 0% 0% / 10%);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 10%), 0 2px 4px -1px hsl(0 0% 0% / 10%);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 10%), 0 4px 6px -1px hsl(0 0% 0% / 10%);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 10%), 0 8px 10px -1px hsl(0 0% 0% / 10%);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 25%);
  /* Illustrations colors */
  --light: var(--brand);
  --light-foreground: var(--brand-foreground);
  --color-1: hsl(0 100% 63%);
  --color-2: hsl(270 100% 63%);
  --color-3: hsl(210 100% 63%);
  --color-4: hsl(195 100% 63%);
  --color-5: hsl(90 100% 63%);

  /* Layout variables */
  --layout-nav-height: 3.5rem; /* 56px */
}

.dark {
  /* Accent colors */
  --brand: oklch(83.6% 0.1177 66.87);
  --brand-foreground: oklch(75.77% 0.159 55.91);

  /* Customized shadcn/ui colors */
  --background-hero: oklch(14.1% 0.005 285.823);
  --foreground-hero: oklch(98.5% 0 0);

  /* Dashboard background */
  --background: oklch(27% 0 106.64);
  --foreground: oklch(81% 0.01 93.01);

  /* Landing page background */
  --background-landing: oklch(18% 0.01 260);
  --foreground-landing: oklch(85% 0.01 95.01);

  --card: oklch(27% 0 106.64);
  --card-foreground: oklch(98% 0.01 95.1);
  --popover: oklch(31% 0 106.6);
  --popover-foreground: oklch(92% 0 106.48);
  --primary: oklch(67% 0.13 38.76);
  --primary-foreground: oklch(100% 0 0);
  --secondary: oklch(98% 0.01 95.1);
  --secondary-foreground: oklch(31% 0 106.6);
  --muted: oklch(22% 0 106.71);
  --muted-foreground: oklch(77% 0.02 99.07);
  --accent: oklch(21% 0.01 95.42);
  --accent-foreground: oklch(97% 0.01 98.88);
  --destructive: oklch(64% 0.21 25.33);
  --destructive-foreground: oklch(100% 0 0);
  --border: oklch(36% 0.01 106.89);
  --input: oklch(43% 0.01 100.22);
  --ring: oklch(36% 0.0728 251.11);
  --chart-1: oklch(56% 0.13 43);
  --chart-2: oklch(69% 0.16 290.41);
  --chart-3: oklch(21% 0.01 95.42);
  --chart-4: oklch(31% 0.05 289.32);
  --chart-5: oklch(56% 0.13 42.06);
  --sidebar: oklch(24% 0 67.71);
  --sidebar-foreground: oklch(81% 0.01 93.01);
  --sidebar-primary: oklch(33% 0 0);
  --sidebar-primary-foreground: oklch(99% 0 0);
  --sidebar-accent: oklch(17% 0 106.62);
  --sidebar-accent-foreground: oklch(81% 0.01 93.01);
  --sidebar-border: oklch(94% 0 0);
  --sidebar-ring: oklch(77% 0 0);
  --font-sans: Inter, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: Mona_Sans, monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 5%);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 5%);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 10%), 0 1px 2px -1px hsl(0 0% 0% / 10%);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 10%), 0 1px 2px -1px hsl(0 0% 0% / 10%);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 10%), 0 2px 4px -1px hsl(0 0% 0% / 10%);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 10%), 0 4px 6px -1px hsl(0 0% 0% / 10%);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 10%), 0 8px 10px -1px hsl(0 0% 0% / 10%);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 25%);

  /* Illustrations colors */
  --light: var(--foreground);
  --light-foreground: var(--foreground);

  /* Layout variables */
  --layout-nav-height: 3.5rem; /* 56px */
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
