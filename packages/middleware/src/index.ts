/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * index.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

// Import functions for internal use
import { createCorsMiddleware } from './cors'
import { createErrorHandler } from './error-handler-simple'
import { createRequestIdMiddleware, createLoggingMiddleware } from './logging'
import { createAuthMiddleware } from './auth'
import type { CorsConfig, LoggingConfig, AuthConfig } from './types'

// Export all types
export type {
  BaseBindings,
  BaseVariables,
  BaseContext,
  UserSession,
  EnvironmentConfig,
  CorsConfig,
  ErrorResponse,
  SuccessResponse,
  RateLimitConfig,
  CloudflareRateLimitConfig,
  CloudflareRateLimiter,
  LoggingConfig,
  AuthConfig,
  MiddlewareFactory,
  ErrorHandlerFunction,
  LogEntry,
  HealthCheckResult,
} from './types'

// Export CORS middleware
export {
  createCorsMiddleware,
  createPublicCorsMiddleware,
  createStrictCorsMiddleware,
  createCdnCorsMiddleware,
} from './cors'

// Export error handling
export {
  LibraError,
  CommonErrorCodes,
  createErrorHandler,
  CommonErrors,
} from './error-handler-simple'
export type { CommonErrorCode } from './error-handler-simple'

// Export logging middleware
export {
  createRequestIdMiddleware,
  createLoggingMiddleware,
  createLogger,
  createPerformanceLoggingMiddleware,
} from './logging'

// Export rate limiting middleware
export {
  createCloudflareRateLimitMiddleware,
  createCloudflareRateLimitMiddlewareFromEnv,
  createCloudflareUserRateLimitMiddleware,
  createCloudflareEndpointRateLimitMiddleware,
  createCloudflareUploadRateLimitMiddleware,
  createCloudflareAuthRateLimitMiddleware,
  CloudflareRateLimitPresets,
} from './rate-limit'

// Export authentication middleware
export {
  createAuthMiddleware,
  createOptionalAuthMiddleware,
  createRoleAuthMiddleware,
  createOrgAuthMiddleware,
  createApiKeyAuthMiddleware,
  AuthUtils,
} from './auth'

