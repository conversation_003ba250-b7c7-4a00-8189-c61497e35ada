{"name": "@libra/email", "version": "0.0.0", "private": true, "description": "Email templates and utilities for Libra", "main": "index.ts", "types": "./index.ts", "type": "module", "scripts": {"build": "tsup", "clean": "rm -rf dist .turbo node_modules", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --", "update": "bun update"}, "dependencies": {"@react-email/components": "^0.3.2", "@react-email/tailwind": "^1.2.2", "@t3-oss/env-nextjs": "^0.11.1", "resend": "^4.7.0", "zod": "^3.25.76"}, "devDependencies": {"@libra/typescript-config": "*"}}