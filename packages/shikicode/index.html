<!--
  ~ SPDX-License-Identifier: AGPL-3.0-only
  ~ index.html
  ~ Copyright (C) 2025 Nextify Limited
  ~
  ~ This program is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU Affero General Public License as
  ~ published by the Free Software Foundation, either version 3 of the
  ~ License, or (at your option) any later version.
  ~
  ~ This program is distributed in the hope that it will be useful,
  ~ but WITHOUT ANY WARRANTY; without even the implied warranty of
  ~ MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
  ~ GNU Affero General Public License for more details.
  ~
  ~ You should have received a copy of the GNU Affero General Public License
  ~ along with this program. If not, see <https://www.gnu.org/licenses/>.
  ~
  -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>ki Editor</title>
    <style>
        html, body {
            margin: 0;
            padding: 0;
        }

        .main {
            display: grid;
            justify-items: center;
            margin: 100px;
        }

        .editor-header {
            display: flex;
            position: absolute;
            top: -40px;
            align-items: center;
            gap: 20px;
            box-sizing: border-box;
            border-radius: 8px 8px 0 0;
            background-color: inherit;
            padding-inline: 20px;
            width: 100%;
            height: 40px;
            color: inherit;
        }

        .editor-header select {
            border-color: transparent;
            background: inherit;
            color: inherit;
        }

        .editor-footer {
            position: absolute;
            bottom: -8px;
            border-radius: 0 0 8px 8px;
            background-color: inherit;
            width: 100%;
            height: 8px;
        }

        #container {
            width: 1000px;
            height: 800px;
            font-size: 14px;
            line-height: 1.7;
        }
    </style>
</head>

<body>
<main class="main">
    <div id="container">
        <header class="editor-header">
            <select id="lang_list"></select>
            <select id="theme_list"></select>
        </header>
        <footer class="editor-footer"></footer>
    </div>
</main>
<script type="importmap">
    {
        "imports": {
            "shiki": "https://cdn.jsdelivr.net/npm/shiki@3.2.2/+esm",
            "shikicode": "./lib/index.js",
            "shikicode/plugins": "./lib/plugins/index.js"
        }
    }
</script>
<script type="module">
    import { bundledLanguagesInfo, bundledThemesInfo } from "shiki";

    bundledLanguagesInfo.forEach((lang) => {
        const option = document.createElement("option");
        option.value = lang.id;
        option.textContent = lang.name;
        lang_list.append(option);
    });

    bundledThemesInfo.forEach((theme) => {
        const option = document.createElement("option");
        option.value = theme.id;
        option.textContent = theme.displayName;
        theme_list.append(option);
    });

    lang_list.addEventListener("change", (e) => {
        editor.updateOptions({
            language: e.target.value,
        });
    });
    theme_list.addEventListener("change", (e) => {
        editor.updateOptions({
            theme: e.target.value,
        });
    });

    theme_list.value = "github-dark";
    lang_list.value = "tsx";
</script>
<script type="module" id="app_script">
    // ^^^^^^^^^    ^^^^^^^^^^ The selectors are not part of shikicode
    import { createHighlighter } from "shiki";
    import { shikiCode } from "shikicode";
    import { autoload, hookClosingPairs, hookTab, comments } from "shikicode/plugins";

    // declare your theme and language
    const theme = "github-dark";
    const language = "tsx";

    // get the highlighter
    const h = await createHighlighter({ langs: [language], themes: [theme] });

    const editor = shikiCode()
        // Optionally, you can config some options or add plugins
        // by using the `withOptions` and `withPlugins` method
        .withPlugins(
            comments({ language: "tsx", lineComment: "//" }),
            // You can add your own plugins here as well
            // `hookClosingPairs` will automatically close the brackets, braces, etc.
            // Try to type `(`, `[`, or `{` in the editor
            hookClosingPairs(),
            // `hookTab` will automatically indent the code when you press the tab key
            // Try to select random text and press the tab key
            hookTab,
            // `autoload` is used to automatically load theme and language,
            // Normally it is not used unless you are building a playground like this
            autoload,
        )
        .create(container, h, {
            // Optionally, you can pass some default value to the editor,
            // such as "Hello, ShikiCode 👋!"
            value: some_default_value,
            language,
            theme,
        });

    // You can check the editor instance in the console
    window.editor = editor;
</script>
<script>
    var some_default_value = app_script.textContent.trim();
</script>
</body>
</html>