{"compilerOptions": {"lib": ["ESNext", "DOM"], "target": "ESNext", "module": "ESNext", "moduleDetection": "force", "jsx": "react-jsx", "allowJs": true, "moduleResolution": "bundler", "verbatimModuleSyntax": true, "declaration": true, "strict": true, "skipLibCheck": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": false, "noUnusedParameters": false, "noPropertyAccessFromIndexSignature": false, "outDir": "./dist", "types": ["@cloudflare/workers-types"]}, "include": ["*.ts", "src/**/*.ts"], "exclude": ["dist", "dist/**/*", "build.ts", "node_modules"]}