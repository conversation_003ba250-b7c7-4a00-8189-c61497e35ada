/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * schema.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

import type { AuthPluginSchema } from "better-auth";
import type { FieldAttribute, FieldType } from "better-auth/db";
import type { CloudflarePluginOptions } from "./types";

/**
 * Type for geolocation database fields
 */
type GeolocationFields = {
    [x: string]: FieldAttribute<FieldType>;
};

/**
 * Database fields for Cloudflare geolocation
 */
const geolocationFields: GeolocationFields = {
    country: {
        type: "string",
        required: false,
        input: false,
    },
    region: {
        type: "string",
        required: false,
        input: false,
    }
};

/**
 * Generates database schema for Cloudflare plugin
 *
 * @param options - Plugin configuration
 * @returns Schema with geolocation fields
 */
export const schema = (options: CloudflarePluginOptions) => {
    return {
        session: {
            fields:
                options.geolocationTracking === undefined || options.geolocationTracking === true
                    ? geolocationFields
                    : {},
        },
    } satisfies AuthPluginSchema;
};