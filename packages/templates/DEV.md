# Libra Templates Package Directory Structure

```bash
packages/templates/
├── DEV-ZH.md                 # Documentation file
├── package.json              # Package dependencies and script definitions
├── tsconfig.json             # TypeScript configuration
├── README.md                 # Package documentation
├── index.ts                  # Template configuration exports
├── types.ts                  # Template type definitions
└── vite-shadcn-template.ts   # Vite+shadcn UI template structure definition
```
